#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试滚动修复效果的简单脚本
"""

import tkinter as tk
import time

class ScrollFixTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("滚动修复测试")
        self.root.geometry("600x400")
        
        # 初始化动画相关变量
        self._animation_active = False
        self._scroll_animation_id = None
        self._last_frame_time = 0
        
        self.create_interface()
        
    def create_interface(self):
        """创建测试界面"""
        # 说明标签
        info_label = tk.Label(self.root, 
                             text="测试滚动动画修复效果\n点击按钮测试连续滚动，观察是否有错误",
                             font=('Arial', 12), pady=10)
        info_label.pack()
        
        # 测试按钮
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=20)
        
        tk.Button(button_frame, text="快速连续滚动测试", 
                 command=self.test_rapid_scroll, 
                 font=('Arial', 10), padx=20, pady=10).pack(side=tk.LEFT, padx=10)
        
        tk.Button(button_frame, text="中断动画测试", 
                 command=self.test_interrupt_animation,
                 font=('Arial', 10), padx=20, pady=10).pack(side=tk.LEFT, padx=10)
        
        # 创建滚动区域
        self.create_scroll_area()
        
        # 状态显示
        self.status_var = tk.StringVar(value="准备就绪")
        status_label = tk.Label(self.root, textvariable=self.status_var, 
                               font=('Arial', 10), fg='blue')
        status_label.pack(pady=10)
        
    def create_scroll_area(self):
        """创建可滚动区域"""
        # 滚动条
        self.scrollbar = tk.Scrollbar(self.root, orient=tk.VERTICAL)
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 画布
        self.canvas = tk.Canvas(self.root, bg='lightblue', 
                               yscrollcommand=self.scrollbar.set)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.scrollbar.config(command=self.canvas.yview)
        
        # 内容
        self.content_frame = tk.Frame(self.canvas)
        self.canvas_window = self.canvas.create_window(0, 0, anchor="nw", 
                                                      window=self.content_frame)
        
        # 添加内容
        for i in range(50):
            label = tk.Label(self.content_frame, 
                           text=f"测试行 {i+1} - 用于测试滚动修复效果",
                           bg='white', relief='ridge', pady=5)
            label.pack(fill=tk.X, padx=5, pady=2)
        
        # 绑定事件
        self.content_frame.bind('<Configure>', self.on_frame_configure)
        self.canvas.bind('<Configure>', self.on_canvas_configure)
        
    def on_frame_configure(self, event):
        """更新滚动区域"""
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
    
    def on_canvas_configure(self, event):
        """调整内容框架宽度"""
        canvas_width = event.width
        self.canvas.itemconfig(self.canvas_window, width=canvas_width)
    
    def test_rapid_scroll(self):
        """测试快速连续滚动"""
        self.status_var.set("开始快速连续滚动测试...")
        
        # 快速连续触发多个滚动动画
        positions = [0.0, 0.3, 0.7, 1.0, 0.5, 0.2, 0.8]
        
        for i, pos in enumerate(positions):
            # 延迟触发，模拟快速连续操作
            self.root.after(i * 200, lambda p=pos: self.smooth_scroll_to(p, 1.0))
        
        # 更新状态
        self.root.after(len(positions) * 200 + 1000, 
                       lambda: self.status_var.set("快速连续滚动测试完成"))
    
    def test_interrupt_animation(self):
        """测试中断动画"""
        self.status_var.set("开始中断动画测试...")
        
        # 开始一个长动画
        self.smooth_scroll_to(1.0, 3.0)
        
        # 500ms后中断并开始新动画
        self.root.after(500, lambda: self.smooth_scroll_to(0.0, 2.0))
        
        # 再500ms后再次中断
        self.root.after(1000, lambda: self.smooth_scroll_to(0.5, 1.0))
        
        # 更新状态
        self.root.after(3000, lambda: self.status_var.set("中断动画测试完成"))
    
    def smooth_scroll_to(self, target_position, duration):
        """修复后的平滑滚动方法"""
        # 获取当前位置
        current_position = self.canvas.yview()[0]
        
        # 如果位置相同，无需滚动
        if abs(current_position - target_position) < 0.001:
            return
        
        # 记录初始状态
        start_position = current_position
        distance = target_position - current_position
        start_time = time.time()
        
        # 安全取消之前的动画（修复后的版本）
        if hasattr(self, '_scroll_animation_id') and self._scroll_animation_id is not None:
            try:
                self.root.after_cancel(self._scroll_animation_id)
            except ValueError:
                # 如果ID无效，忽略错误
                pass
            self._scroll_animation_id = None
        
        # 初始化动画状态
        self._animation_active = True
        self._last_frame_time = start_time
        
        print(f"开始滚动: {start_position:.3f} -> {target_position:.3f}, 时长: {duration}s")
        
        def animation_frame():
            if not self._animation_active:
                return
                
            current_time = time.time()
            elapsed = current_time - start_time
            
            # 检查是否完成动画
            if elapsed >= duration:
                # 动画结束，清理状态
                self._animation_active = False
                self._scroll_animation_id = None
                self.canvas.yview_moveto(target_position)
                print(f"滚动完成: {target_position:.3f}")
                return
            
            # 计算进度和新位置
            progress = min(elapsed / duration, 1.0)
            ease = self._advanced_easing(progress)
            new_position = start_position + distance * ease
            
            # 应用新位置
            self.canvas.yview_moveto(new_position)
            
            # 继续动画
            self._scroll_animation_id = self.root.after(16, animation_frame)
        
        # 开始动画
        animation_frame()
    
    def _advanced_easing(self, t):
        """高级缓动函数"""
        if t <= 0:
            return 0
        if t >= 1:
            return 1
        return t * t * (3.0 - 2.0 * t) * (1.0 + 0.5 * t * (1.0 - t))
    
    def run(self):
        """运行测试"""
        print("滚动修复测试启动")
        print("测试项目:")
        print("1. 快速连续滚动 - 验证动画ID管理")
        print("2. 中断动画 - 验证动画取消机制")
        print("观察控制台输出，确认没有ValueError错误")
        self.root.mainloop()

if __name__ == "__main__":
    test = ScrollFixTest()
    test.run()
